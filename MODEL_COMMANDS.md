# Mars Terrain Segmentation Model Commands

This document provides common commands for training and evaluating Mars terrain segmentation models for quick reference.

## Visualization Setup (Required Before Training with Visualization)

Before training or evaluation with visualization, start the Visdom server:

```bash
python -m visdom.server -port 8097
```

Then access the visualization interface in your browser at: `http://localhost:8097`

**Note**: If you don't need visualization, you can remove the `--enable_vis` and `--vis_port 8097` parameters from the training commands

## Performance Evaluation (NEW FEATURE)

### FPS, FLOPs, and Parameters Evaluation

You can now evaluate model performance metrics (FPS, FLOPs, Parameters) during testing by adding the `--eval_performance` flag to any test command.

**Additional Performance Parameters:**
- `--eval_performance`: Enable performance evaluation

**Example with Performance Evaluation:**
```bash
python main.py --model segformer_b2 --dataset AI4Mars --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_segformer_b2_AI4Mars.pth --test_only --save_val_results --eval_performance
```

**Note:** The performance evaluation will automatically use the same `--crop_size` and `--batch_size` parameters as specified in your command. FPS measurement uses 100 iterations for stable results.

**Performance Results:**
- Console output with detailed performance metrics
- Automatic saving to `performance_results_{model}_{dataset}.txt`
- Includes: FPS, Parameters count, Model size, FLOPs, GPU memory usage

**Requirements:**
Make sure to install the required packages:
```bash
pip install thop psutil
```

## Model Evaluation Commands

### DeepLabV3+ Models Evaluation

The following commands are used to evaluate DeepLabV3+ models trained with 512×512 image size and batch size 4. Make sure to replace the checkpoint path with the path to your trained model.

**Important**: For evaluation, you must specify a valid checkpoint path with `--ckpt`. The `--test_only` flag enables test mode.

#### AI4Mars Dataset Evaluation

##### Evaluation on testM1
```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_deeplabv3plus_resnet101_AI4Mars.pth --test_only --save_val_results --test_split testM1
```

##### Evaluation on testM2
```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_deeplabv3plus_resnet101_AI4Mars.pth --test_only --save_val_results --test_split testM2
```

##### Evaluation on testM3
```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_deeplabv3plus_resnet101_AI4Mars.pth --test_only --save_val_results --test_split testM3
```

##### Evaluation on Standard Test Set
```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_deeplabv3plus_resnet101_AI4Mars.pth --test_only --save_val_results
```

#### AI4Mars-SMI Dataset Evaluation
```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars-SMI --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_deeplabv3plus_resnet101_AI4Mars-SMI.pth --test_only --save_val_results
```

#### LabelMars6 Dataset Evaluation
```bash
python main.py --model deeplabv3plus_resnet101 --dataset LabelMars6 --num_classes 6 --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_deeplabv3plus_resnet101_LabelMars6.pth --test_only --save_val_results
```

### SegFormer Models Evaluation

#### SegFormer-B0 Evaluation
```bash
# AI4Mars
python main.py --model segformer_b0 --dataset AI4Mars --num_classes 5 --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_segformer_b0_AI4Mars.pth --test_only --save_val_results

# AI4Mars-SMI
python main.py --model segformer_b0 --dataset AI4Mars-SMI --num_classes 5 --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_segformer_b0_AI4Mars-SMI.pth --test_only --save_val_results

# LabelMars6
python main.py --model segformer_b0 --dataset LabelMars6 --num_classes 6 --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_segformer_b0_LabelMars6.pth --test_only --save_val_results
```

#### SegFormer-B1 Evaluation
```bash
# AI4Mars
python main.py --model segformer_b1 --dataset AI4Mars --num_classes 5 --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_segformer_b1_AI4Mars.pth --test_only --save_val_results

# AI4Mars-SMI
python main.py --model segformer_b1 --dataset AI4Mars-SMI --num_classes 5 --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_segformer_b1_AI4Mars-SMI.pth --test_only --save_val_results

# LabelMars6
python main.py --model segformer_b1 --dataset LabelMars6 --num_classes 6 --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_segformer_b1_LabelMars6.pth --test_only --save_val_results
```

#### SegFormer-B2 Evaluation
```bash
# AI4Mars
python main.py --model segformer_b2 --dataset AI4Mars --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_segformer_b2_AI4Mars.pth --test_only --save_val_results

# AI4Mars-SMI
python main.py --model segformer_b2 --dataset AI4Mars-SMI --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_segformer_b2_AI4Mars-SMI.pth --test_only --save_val_results

# LabelMars6
python main.py --model segformer_b2 --dataset LabelMars6 --num_classes 6 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_segformer_b2_LabelMars6.pth --test_only --save_val_results
```

#### SegFormer-B3 Evaluation
```bash
# AI4Mars
python main.py --model segformer_b3 --dataset AI4Mars --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_segformer_b3_AI4Mars.pth --test_only --save_val_results --eval_performance

# AI4Mars-SMI
python main.py --model segformer_b3 --dataset AI4Mars-SMI --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_segformer_b3_AI4Mars-SMI.pth --test_only --save_val_results --eval_performance

# LabelMars6
python main.py --model segformer_b3 --dataset LabelMars6 --num_classes 6 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_segformer_b3_LabelMars6.pth --test_only --save_val_results --eval_performance
```

### Mask2Former Models Evaluation

#### Mask2Former Swin-Tiny Evaluation
```bash
# AI4Mars
python main.py --model mask2former_swin_tiny --dataset AI4Mars --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_mask2former_swin_tiny_AI4Mars.pth --test_only --save_val_results

# AI4Mars-SMI
python main.py --model mask2former_swin_tiny --dataset AI4Mars-SMI --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_mask2former_swin_tiny_AI4Mars-SMI.pth --test_only --save_val_results

# LabelMars6
python main.py --model mask2former_swin_tiny --dataset LabelMars6 --num_classes 6 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_mask2former_swin_tiny_LabelMars6.pth --test_only --save_val_results
```

#### Mask2Former Swin-Tiny ADE Evaluation
```bash
# AI4Mars
python main.py --model mask2former_swin_tiny_ade --dataset AI4Mars --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_mask2former_swin_tiny_ade_AI4Mars.pth --test_only --save_val_results

# AI4Mars-SMI
python main.py --model mask2former_swin_tiny_ade --dataset AI4Mars-SMI --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_mask2former_swin_tiny_ade_AI4Mars-SMI.pth --test_only --save_val_results

# LabelMars6
python main.py --model mask2former_swin_tiny_ade --dataset LabelMars6 --num_classes 6 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_mask2former_swin_tiny_ade_LabelMars6.pth --test_only --save_val_results
```

#### Mask2Former Swin-Small ADE Evaluation
```bash
# AI4Mars
python main.py --model mask2former_swin_small_ade --dataset AI4Mars --num_classes 5 --crop_size 512 --batch_size 4 --ckpt "D:\复现\模型\Paper Models\Mask2former-Swin-S-512-4-Original-Ai4mars\bestEarlystop_mask2former_swin_small_ade_AI4Mars_os16.pth" --test_only --save_val_results --eval_performance --vis_port 8097 --test_split testM1  

python main.py --model mask2former_swin_small_ade --dataset AI4Mars --num_classes 5 --crop_size 512 --batch_size 4 --ckpt "D:\复现\模型\Paper Models\Mask2former-Swin-S-512-4-Original-Ai4mars\bestEarlystop_mask2former_swin_small_ade_AI4Mars_os16.pth" --test_only --save_val_results --eval_performance --vis_port 8097 --test_split testM3          

# AI4Mars-SMI       
python main.py --model mask2former_swin_small_ade --dataset AI4Mars-SMI --num_classes 5 --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_mask2former_swin_small_ade_AI4Mars-SMI.pth --test_only --save_val_results --eval_performance --vis_port 8097 --test_split testM1

# LabelMars6
python main.py --model mask2former_swin_small_ade --dataset LabelMars6 --num_classes 6 --crop_size 512 --batch_size 4 --ckpt checkpoints/latest_mask2former_swin_small_ade_LabelMars6.pth --test_only --save_val_results --eval_performance --vis_port 8097 --test_split testM1
```

### UPerNet Models Evaluation

#### UPerNet Swin-Tiny Evaluation
```bash
# AI4Mars
python main.py --model upernet_swin_tiny --dataset AI4Mars --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_upernet_swin_tiny_AI4Mars.pth --test_only --save_val_results

# AI4Mars-SMI
python main.py --model upernet_swin_tiny --dataset AI4Mars-SMI --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_upernet_swin_tiny_AI4Mars-SMI.pth --test_only --save_val_results

# LabelMars6
python main.py --model upernet_swin_tiny --dataset LabelMars6 --num_classes 6 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_upernet_swin_tiny_LabelMars6.pth --test_only --save_val_results
```

#### UPerNet Swin-Small Evaluation
```bash
# AI4Mars
python main.py --model upernet_swin_small --dataset AI4Mars --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_upernet_swin_small_AI4Mars.pth --test_only --save_val_results

# AI4Mars-SMI
python main.py --model upernet_swin_small --dataset AI4Mars-SMI --num_classes 5 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_upernet_swin_small_AI4Mars-SMI.pth --test_only --save_val_results

# LabelMars6
python main.py --model upernet_swin_small --dataset LabelMars6 --num_classes 6 --crop_size 512 --batch_size 2 --ckpt checkpoints/latest_upernet_swin_small_LabelMars6.pth --test_only --save_val_results


```

## Model Training Commands

### DeepLabV3+ Models Training

#### Training DeepLabV3+ (ResNet101) on AI4Mars
```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --output_stride 16 --total_epochs 200 --enable_vis --vis_port 8097
```

#### Training DeepLabV3+ (ResNet101) on AI4Mars-SMI
```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars-SMI --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --output_stride 16 --total_epochs 200 --enable_vis --vis_port 8097
```

#### Training DeepLabV3+ (ResNet101) on LabelMars6
```bash
python main.py --model deeplabv3plus_resnet101 --dataset LabelMars6 --num_classes 6 --lr 0.001 --batch_size 4 --crop_size 512 --output_stride 16 --total_epochs 200 --enable_vis --vis_port 8097
```

#### Training DeepLabV3+ (ResNet101) with Separable Convolution
```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --output_stride 16 --total_epochs 200 --enable_vis --vis_port 8097--test_split testM1 --separable_conv
```

### SegFormer Models Training

#### SegFormer-B0 Training
```bash
# AI4Mars
python main.py --model segformer_b0 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# AI4Mars-SMI
python main.py --model segformer_b0 --dataset AI4Mars-SMI --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# LabelMars6
python main.py --model segformer_b0 --dataset LabelMars6 --num_classes 6 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097
```

#### SegFormer-B1 Training
```bash
# AI4Mars
python main.py --model segformer_b1 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# AI4Mars-SMI
python main.py --model segformer_b1 --dataset AI4Mars-SMI --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# LabelMars6
python main.py --model segformer_b1 --dataset LabelMars6 --num_classes 6 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097
```

#### SegFormer-B2 Training
```bash
# AI4Mars
python main.py --model segformer_b2 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# AI4Mars-SMI
python main.py --model segformer_b2 --dataset AI4Mars-SMI --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# LabelMars6
python main.py --model segformer_b2 --dataset LabelMars6 --num_classes 6 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097
```

#### SegFormer-B3 Training
```bash
# AI4Mars
python main.py --model segformer_b3 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# AI4Mars-SMI
python main.py --model segformer_b3 --dataset AI4Mars-SMI --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# LabelMars6
python main.py --model segformer_b3 --dataset LabelMars6 --num_classes 6 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097
```

### Mask2Former Models Training

#### Mask2Former Swin-Tiny Training
```bash
# AI4Mars
python main.py --model mask2former_swin_tiny --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# AI4Mars-SMI
python main.py --model mask2former_swin_tiny --dataset AI4Mars-SMI --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# LabelMars6
python main.py --model mask2former_swin_tiny --dataset LabelMars6 --num_classes 6 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097
```

#### Mask2Former Swin-Tiny ADE Training
```bash
# AI4Mars
python main.py --model mask2former_swin_tiny_ade --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# AI4Mars-SMI
python main.py --model mask2former_swin_tiny_ade --dataset AI4Mars-SMI --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# LabelMars6
python main.py --model mask2former_swin_tiny_ade --dataset LabelMars6 --num_classes 6 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097
```

#### Mask2Former Swin-Small ADE Training
```bash
# AI4Mars
python main.py --model mask2former_swin_small_ade --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 1 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# AI4Mars-SMI
python main.py --model mask2former_swin_small_ade --dataset AI4Mars-SMI --num_classes 5 --lr 0.001 --batch_size 1 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# LabelMars6
python main.py --model mask2former_swin_small_ade --dataset LabelMars6 --num_classes 6 --lr 0.001 --batch_size 1 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097
```

### UPerNet Models Training

#### UPerNet Swin-Tiny Training
```bash
# AI4Mars
python main.py --model upernet_swin_tiny --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# AI4Mars-SMI
python main.py --model upernet_swin_tiny --dataset AI4Mars-SMI --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# LabelMars6
python main.py --model upernet_swin_tiny --dataset LabelMars6 --num_classes 6 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097
```

#### UPerNet Swin-Small Training
```bash
# AI4Mars
python main.py --model upernet_swin_small --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# AI4Mars-SMI
python main.py --model upernet_swin_small --dataset AI4Mars-SMI --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# LabelMars6
python main.py --model upernet_swin_small --dataset LabelMars6 --num_classes 6 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097
```

#### UPerNet Swin-Base Training
```bash
# AI4Mars
python main.py --model upernet_swin_base --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# AI4Mars-SMI
python main.py --model upernet_swin_base --dataset AI4Mars-SMI --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097

# LabelMars6
python main.py --model upernet_swin_base --dataset LabelMars6 --num_classes 6 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --enable_vis --vis_port 8097
```

### Training U-Net (ResNet101)

This command trains a U-Net model with ResNet101 backbone:

```bash
python main.py --model unet_resnet101 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --output_stride 16 --total_epochs 200 --enable_vis --vis_port 8097
```

### Resuming Training from a Checkpoint

To resume training from a previously saved checkpoint:

```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --output_stride 16 --total_epochs 200 --enable_vis --vis_port 8097 --ckpt checkpoints/latest_deeplabv3plus_resnet101_AI4Mars.pth --continue_training
```

**Parameter explanation**:
- `--model`: Model architecture (e.g., segformer_b0, mask2former_swin_tiny, upernet_swin_tiny)
- `--dataset`: Dataset name (AI4Mars, AI4Mars-SMI, LabelMars6)
- `--num_classes`: Number of segmentation classes (5 for AI4Mars/AI4Mars-SMI, 6 for LabelMars6)
- `--lr`: Learning rate (0.001, automatically adjusted to 0.0001 for Transformer models)
- `--batch_size`: Batch size for training (varies by model complexity)
- `--crop_size`: Input image size (512×512)
- `--output_stride`: Output stride of the backbone (16, not used for Transformer models)
- `--total_epochs`: Total number of training epochs (200)
- `--enable_vis`: Enable Visdom visualization
- `--vis_port`: Visdom server port (8097)

## Training Commands with Different Loss Functions

### Training with Focal Loss

#### DeepLabV3+ with Focal Loss
```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --output_stride 16 --total_epochs 200 --loss_type focal_loss --enable_vis --vis_port 8097
```

#### SegFormer with Focal Loss
```bash
python main.py --model segformer_b0 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --loss_type focal_loss --enable_vis --vis_port 8097
```

#### Mask2Former with Focal Loss
```bash
python main.py --model mask2former_swin_tiny --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --loss_type focal_loss --enable_vis --vis_port 8097
```

#### UPerNet with Focal Loss
```bash
python main.py --model upernet_swin_tiny --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --loss_type focal_loss --enable_vis --vis_port 8097
```

### Training with Dice Loss

#### DeepLabV3+ with Dice Loss
```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --output_stride 16 --total_epochs 200 --loss_type dice_loss --enable_vis --vis_port 8097
```

#### SegFormer with Dice Loss
```bash
python main.py --model segformer_b0 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --loss_type dice_loss --enable_vis --vis_port 8097
```

#### Mask2Former with Dice Loss
```bash
python main.py --model mask2former_swin_tiny --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --loss_type dice_loss --enable_vis --vis_port 8097
```

#### UPerNet with Dice Loss
```bash
python main.py --model upernet_swin_tiny --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --loss_type dice_loss --enable_vis --vis_port 8097
```

## Resuming Training from Checkpoint

### DeepLabV3+ Resume Training
```bash
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --output_stride 16 --total_epochs 200 --ckpt checkpoints/latest_deeplabv3plus_resnet101_AI4Mars_os16.pth --continue_training --enable_vis --vis_port 8097
```

### SegFormer Resume Training
```bash
python main.py --model segformer_b0 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200 --ckpt checkpoints/latest_segformer_b0_AI4Mars.pth --continue_training --enable_vis --vis_port 8097
```

### Mask2Former Resume Training
```bash
python main.py --model mask2former_swin_tiny --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --ckpt checkpoints/latest_mask2former_swin_tiny_AI4Mars.pth --continue_training --enable_vis --vis_port 8097
```

### UPerNet Resume Training
```bash
python main.py --model upernet_swin_tiny --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 2 --crop_size 512 --total_epochs 200 --ckpt checkpoints/latest_upernet_swin_tiny_AI4Mars.pth --continue_training --enable_vis --vis_port 8097
```

## Model-Specific Recommendations

### Batch Size Guidelines
- **SegFormer B0-B1**: batch_size 4 (lightweight models)
- **SegFormer B2-B5**: batch_size 2 (larger models)
- **Mask2Former Tiny/Small**: batch_size 2
- **Mask2Former Base/Large**: batch_size 1
- **UPerNet Tiny/Small**: batch_size 2
- **UPerNet Base**: batch_size 1
- **DeepLabV3+**: batch_size 4-8 (depending on backbone)

### Learning Rate Notes
- Transformer models (SegFormer, Mask2Former, UPerNet) automatically use 0.1x of specified learning rate
- CNN models (DeepLabV3+, U-Net) use the specified learning rate directly
- Recommended starting learning rate: 0.001

### Memory Requirements
- **Low Memory**: SegFormer B0-B1, Mask2Former Tiny
- **Medium Memory**: SegFormer B2-B3, UPerNet Tiny/Small, DeepLabV3+
- **High Memory**: SegFormer B4-B5, Mask2Former Base/Large, UPerNet Base

## Important Notes

1. Ensure the dataset is placed in the correct directory structure
2. For large models or high-resolution images, adjust batch size to fit GPU memory
3. When using `--enable_vis` parameter, ensure the Visdom server is running
4. When using `--test_only` parameter, ensure the correct model checkpoint path is specified
5. Transformer models require the `transformers` library to be installed
6. First-time usage of new models will download pre-trained weights automatically
7. For batch_size=1 training, the system automatically handles BatchNorm compatibility issues
